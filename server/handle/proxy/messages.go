package proxy

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"log"

	mcprouter "github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/jsonrpc"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/proxy"
	"github.com/labstack/echo/v4"
)

// Messages is a handler for the messages endpoint
func Messages(c echo.Context) error {
	var err error
	ctx := proxy.GetSSEContext(c)
	if ctx == nil {
		return c.String(http.StatusInternalServerError, "Failed to get SSE context")
	}

	// 新增：优先通过sessionid查找第三方messages地址
	sessionID := c.QueryParam("sessionid")
	if sessionID == "" {
		sessionID = c.FormValue("sessionid")
	}
	if sessionID == "" {
		sessionID = c.Param("sessionid")
	}
	if sessionID != "" && ctx != nil {
		session := ctx.GetSession(sessionID)
		if session != nil && session.ThirdPartyMessagesURL != "" {
			// ====== 积分校验逻辑（第三方代理前，预检查） ======
			request, _ := ctx.GetJSONRPCRequest()
			checkResult, _ := validatePointsForToolCall(c.Request().Context(), request, session.ProxyInfo().ServerUUID, session.ProxyInfo().UserID)
			if checkResult.ErrorType != "" {
				// 获取serverkey信息用于完整记录
				var serverkey *mcprouter.Serverkey
				if session.ProxyInfo().ServerKey != "" {
					serverkey = &mcprouter.Serverkey{
						ServerKey:  session.ProxyInfo().ServerKey,
						ServerUUID: session.ProxyInfo().ServerUUID,
					}
				}

				if err := handlePointsCheckFailure(c.Request().Context(), checkResult, c, serverkey, session.ServerConfig()); err != nil {
					return c.String(http.StatusForbidden, err.Error())
				}
			}

			// 设置 proxyInfo 的积分信息（第三方代理）
			currentProxyInfo := session.ProxyInfo()

			// 设置请求方法和时间（第三方代理）
			if request != nil && request.Method == "tools/call" {
				currentProxyInfo.RequestMethod = "tools/call"
				currentProxyInfo.RequestTime = time.Now()
				currentProxyInfo.RequestParams = request.Params
				if request.ID != nil {
					currentProxyInfo.RequestID = fmt.Sprintf("%v", request.ID)
				}
			}

			if checkResult.ToolID != "" && checkResult.ToolRecord.Points != nil {
				currentProxyInfo.ToolName = checkResult.ToolID
				currentProxyInfo.Points = *checkResult.ToolRecord.Points
				log.Printf("[第三方代理] 设置积分信息: ToolName=%s, Points=%d", checkResult.ToolID, *checkResult.ToolRecord.Points)
			} else {
				log.Printf("[第三方代理] 积分信息缺失: ToolID=%s, Points=nil", checkResult.ToolID)
			}

			session.SetProxyInfo(currentProxyInfo)

			// ====== 继续第三方代理逻辑 ======
			thirdPartyUrl := session.ThirdPartyMessagesURL
			if session.ThirdPartyQueryRaw != "" {
				if strings.Contains(thirdPartyUrl, "?") {
					thirdPartyUrl += "&" + session.ThirdPartyQueryRaw
				} else {
					thirdPartyUrl += "?" + session.ThirdPartyQueryRaw
				}
			}
			log.Printf("最终代理到第三方的URL: %s", thirdPartyUrl)
			requestBody, _ := json.Marshal(request)
			reqProxy, _ := http.NewRequest("POST", thirdPartyUrl, strings.NewReader(string(requestBody)))
			// 复制原始请求header
			for k, v := range c.Request().Header {
				if len(v) > 0 {
					reqProxy.Header.Set(k, v[0])
				}
			}
			// 添加DbEnv中的键值对到请求头
			dbEnv := session.ProxyInfo().DbEnv
			if dbEnv != nil && len(dbEnv) > 0 {
				for key, value := range dbEnv {
					reqProxy.Header.Set(key, value)
					log.Printf("添加DbEnv到请求头: %s = %s", key, value)
				}
			}

			client := &http.Client{Timeout: 0}
			resp, err := client.Do(reqProxy)
			if err != nil {
				return c.String(http.StatusBadGateway, "SSE代理失败")
			}
			defer resp.Body.Close()
			c.Response().Header().Set("Content-Type", "application/json")

			// 收集完整响应数据用于积分扣除判断
			var fullResponse []byte
			buf := make([]byte, 4096)
			for {
				n, err := resp.Body.Read(buf)
				if n > 0 {
					fullResponse = append(fullResponse, buf[:n]...)
					c.Response().Write(buf[:n])
					c.Response().Flush()
					log.Printf("代理第三方messages收到数据: %d bytes", n)
				}
				if err != nil {
					if err == io.EOF {
						log.Printf("第三方messages读取完毕（EOF），正常结束")
					} else {
						log.Printf("代理第三方messages读取出错: %v", err)
					}
					break
				}
			}

			// 为第三方代理成功的tools/call创建server_logs
			currentProxyInfo = session.ProxyInfo()
			log.Printf("[第三方代理] 检查RequestMethod: %s, ToolName: %s", currentProxyInfo.RequestMethod, currentProxyInfo.ToolName)

			if currentProxyInfo.RequestMethod == "tools/call" {
				// 设置响应信息
				currentProxyInfo.ResponseTime = time.Now()
				currentProxyInfo.CostTime = currentProxyInfo.ResponseTime.Sub(currentProxyInfo.RequestTime).Milliseconds()

				// 尝试解析响应结果
				var response map[string]interface{}
				if err := json.Unmarshal(fullResponse, &response); err == nil {
					currentProxyInfo.ResponseResult = response
				} else {
					// 如果无法解析为JSON，存储原始字符串
					currentProxyInfo.ResponseResult = string(fullResponse)
				}

				log.Printf("[第三方代理] 准备记录server_logs: RequestMethod=%s, ToolName=%s, Points=%d", currentProxyInfo.RequestMethod, currentProxyInfo.ToolName, currentProxyInfo.Points)
				if err := createServerLogAndUpdateUsage(currentProxyInfo); err != nil {
					log.Printf("[第三方代理] 创建成功日志记录失败: %v", err)
				} else {
					log.Printf("[第三方代理] 成功记录server_logs，积分: %d", currentProxyInfo.Points)
				}
			} else {
				log.Printf("[第三方代理] RequestMethod不是tools/call，跳过server_logs记录")
			}

			// 检查响应并决定是否扣除积分
			if checkResult.ShouldDeduct {
				var response map[string]interface{}
				if err := json.Unmarshal(fullResponse, &response); err == nil {
					if err := deductPointsAfterToolCall(c.Request().Context(), checkResult, response); err != nil {
						log.Printf("第三方代理扣积分失败: %v", err)
					}
				}
			}

			return nil
		}
	}

	// 直接获取key和token参数，兼容query、form和路径参数
	key := c.QueryParam("key")
	token := c.QueryParam("token")
	if key == "" {
		key = c.FormValue("key")
	}
	if token == "" {
		token = c.FormValue("token")
	}
	if key == "" {
		key = c.Param("key")
	}
	if token == "" {
		token = c.Param("token")
	}

	// 查serverkey
	serverkey, err := mcprouter.FindServerkeyByServerKey(key)
	if err == nil && serverkey.SseUrl != "" {
		// 查api_key
		apiKey, err := service.ServiceGroupApp.McpServiceGroup.ApiKeyService.ValidateApiKey(token)
		if err != nil {
			// 直接代理到远端/messages（无积分检查，因为token无效）
			remoteMessagesUrl := strings.Replace(serverkey.SseUrl, "/sse", "/messages", 1)
			headers := http.Header{}
			if serverkey.EnvJson != "" {
				var env map[string]interface{}
				_ = json.Unmarshal([]byte(serverkey.EnvJson), &env)
				for k, v := range env {
					headers.Set(k, fmt.Sprintf("%v", v))
				}
			}
			headers.Set("X-Token", token)
			request, _ := ctx.GetJSONRPCRequest()
			requestBody, _ := json.Marshal(request)
			reqProxy, _ := http.NewRequest("POST", remoteMessagesUrl, strings.NewReader(string(requestBody)))
			reqProxy.Header = headers
			for k, v := range c.Request().Header {
				if len(v) > 0 {
					reqProxy.Header.Set(k, v[0])
				}
			}
			// 添加DbEnv中的键值对到请求头（如果存在session）
			if sessionID != "" {
				if ctx != nil {
					session := ctx.GetSession(sessionID)
					if session != nil {
						dbEnv := session.ProxyInfo().DbEnv
						if dbEnv != nil && len(dbEnv) > 0 {
							for key, value := range dbEnv {
								reqProxy.Header.Set(key, value)
								log.Printf("添加DbEnv到请求头: %s = %s", key, value)
							}
						}
					}
				}
			}
			log.Printf("[MESSAGES代理-直接token兜底] 请求url: %s, headers: %+v", remoteMessagesUrl, reqProxy.Header)
			client := &http.Client{Timeout: 0}
			resp, err := client.Do(reqProxy)
			if err != nil {
				return c.String(http.StatusBadGateway, "SSE代理失败")
			}
			defer resp.Body.Close()
			c.Response().Header().Set("Content-Type", "application/json")
			buf := make([]byte, 4096)
			for {
				n, err := resp.Body.Read(buf)
				if n > 0 {
					c.Response().Write(buf[:n])
					c.Response().Flush()
					if strings.Contains(string(buf[:n]), "event: done") {
						break
					}
				}
				if err != nil {
					break
				}
			}
			return nil
		} else {
			// token有效，需要进行积分检查
			request, _ := ctx.GetJSONRPCRequest()
			if request != nil && request.Method == "tools/call" {
				checkResult, _ := validatePointsForToolCall(c.Request().Context(), request, serverkey.ServerUUID, fmt.Sprintf("%d", apiKey.UserId))
				if checkResult.ErrorType != "" {
					// 积分检查失败，记录日志并返回错误
					if err := handlePointsCheckFailure(c.Request().Context(), checkResult, c, serverkey, nil); err != nil {
						return c.String(http.StatusForbidden, err.Error())
					}
				}

				// 代理到远端/messages
				remoteMessagesUrl := strings.Replace(serverkey.SseUrl, "/sse", "/messages", 1)
				headers := http.Header{}
				if serverkey.EnvJson != "" {
					var env map[string]interface{}
					_ = json.Unmarshal([]byte(serverkey.EnvJson), &env)
					for k, v := range env {
						headers.Set(k, fmt.Sprintf("%v", v))
					}
				}
				headers.Set("X-Token", token)
				requestBody, _ := json.Marshal(request)
				reqProxy, _ := http.NewRequest("POST", remoteMessagesUrl, strings.NewReader(string(requestBody)))
				reqProxy.Header = headers
				for k, v := range c.Request().Header {
					if len(v) > 0 {
						reqProxy.Header.Set(k, v[0])
					}
				}

				log.Printf("[MESSAGES代理-有效token] 请求url: %s, headers: %+v", remoteMessagesUrl, reqProxy.Header)
				client := &http.Client{Timeout: 0}
				resp, err := client.Do(reqProxy)
				if err != nil {
					// 为失败的请求记录server_logs
					if checkResult.ToolID != "" {
						errorMsg := fmt.Sprintf("远程代理请求失败: %v", err)
						tempProxyInfo := createErrorProxyInfo(c, serverkey, nil, checkResult, errorMsg)
						if err := createServerLogAndUpdateUsage(tempProxyInfo); err != nil {
							log.Printf("[MESSAGES代理-有效token] 创建失败日志记录失败: %v", err)
						}
					}
					return c.String(http.StatusBadGateway, "远程代理失败")
				}
				defer resp.Body.Close()

				// 收集完整响应数据用于积分扣除判断
				var fullResponse []byte
				c.Response().Header().Set("Content-Type", "application/json")
				buf := make([]byte, 4096)
				for {
					n, err := resp.Body.Read(buf)
					if n > 0 {
						fullResponse = append(fullResponse, buf[:n]...)
						c.Response().Write(buf[:n])
						c.Response().Flush()
					}
					if err != nil {
						break
					}
				}

				// 为成功的请求记录server_logs
				if checkResult.ToolID != "" {
					var response map[string]interface{}
					var responseForLog interface{}

					if err := json.Unmarshal(fullResponse, &response); err == nil {
						responseForLog = response
						log.Printf("[MESSAGES代理-有效token] 成功解析响应JSON，长度: %d", len(fullResponse))
					} else {
						// JSON解析失败，使用原始字符串
						responseForLog = string(fullResponse)
						log.Printf("[MESSAGES代理-有效token] JSON解析失败，使用原始响应: %v, 长度: %d", err, len(fullResponse))
					}

					tempProxyInfo := createSuccessProxyInfo(c, serverkey, nil, checkResult, responseForLog)
					if err := createServerLogAndUpdateUsage(tempProxyInfo); err != nil {
						log.Printf("[MESSAGES代理-有效token] 创建成功日志记录失败: %v", err)
					} else {
						log.Printf("[MESSAGES代理-有效token] 成功记录server_logs，积分: %d", tempProxyInfo.Points)
					}
				}

				// 检查响应并决定是否扣除积分
				if checkResult.ShouldDeduct {
					var response map[string]interface{}
					if err := json.Unmarshal(fullResponse, &response); err == nil {
						if err := deductPointsAfterToolCall(c.Request().Context(), checkResult, response); err != nil {
							log.Printf("远程代理扣积分失败: %v", err)
						}
					}
				}

				return nil
			} else {
				// 非tools/call请求，直接代理
				remoteMessagesUrl := strings.Replace(serverkey.SseUrl, "/sse", "/messages", 1)
				headers := http.Header{}
				if serverkey.EnvJson != "" {
					var env map[string]interface{}
					_ = json.Unmarshal([]byte(serverkey.EnvJson), &env)
					for k, v := range env {
						headers.Set(k, fmt.Sprintf("%v", v))
					}
				}
				headers.Set("X-Token", token)
				request, _ := ctx.GetJSONRPCRequest()
				requestBody, _ := json.Marshal(request)
				reqProxy, _ := http.NewRequest("POST", remoteMessagesUrl, strings.NewReader(string(requestBody)))
				reqProxy.Header = headers
				for k, v := range c.Request().Header {
					if len(v) > 0 {
						reqProxy.Header.Set(k, v[0])
					}
				}

				log.Printf("[MESSAGES代理-非tools/call] 请求url: %s", remoteMessagesUrl)
				client := &http.Client{Timeout: 0}
				resp, err := client.Do(reqProxy)
				if err != nil {
					return c.String(http.StatusBadGateway, "远程代理失败")
				}
				defer resp.Body.Close()
				c.Response().Header().Set("Content-Type", "application/json")
				buf := make([]byte, 4096)
				for {
					n, err := resp.Body.Read(buf)
					if n > 0 {
						c.Response().Write(buf[:n])
						c.Response().Flush()
					}
					if err != nil {
						break
					}
				}
				return nil
			}
		}
	}

	sessionID = ctx.QueryParam("sessionid")
	if sessionID == "" {
		return ctx.JSONRPCError(jsonrpc.ErrorInvalidParams, nil)
	}

	session := ctx.GetSession(sessionID)
	if session == nil {
		return ctx.JSONRPCError(jsonrpc.ErrorInvalidParams, nil)
	}

	// 本地messages逻辑前的积分预检查
	request, err := ctx.GetJSONRPCRequest()
	if err != nil {
		return ctx.JSONRPCError(jsonrpc.ErrorParseError, nil)
	}

	checkResultLocal, _ := validatePointsForToolCall(c.Request().Context(), request, session.ProxyInfo().ServerUUID, session.ProxyInfo().UserID)
	if checkResultLocal.ErrorType != "" {
		// 获取serverkey信息用于完整记录
		var serverkey *mcprouter.Serverkey
		if session.ProxyInfo().ServerKey != "" {
			serverkey = &mcprouter.Serverkey{
				ServerKey:  session.ProxyInfo().ServerKey,
				ServerUUID: session.ProxyInfo().ServerUUID,
			}
		}

		if err := handlePointsCheckFailure(c.Request().Context(), checkResultLocal, c, serverkey, session.ServerConfig()); err != nil {
			return c.String(http.StatusForbidden, err.Error())
		}
	}

	proxyInfo := session.ProxyInfo()
	sseKey := session.Key()
	proxyInfo.ToolName = checkResultLocal.ToolID
	// 设置积分信息（复用前面查询的工具信息）
	if checkResultLocal.ToolRecord.Points != nil {
		proxyInfo.Points = *checkResultLocal.ToolRecord.Points
		log.Printf("[本地Messages] 设置积分信息: ToolName=%s, Points=%d", checkResultLocal.ToolID, *checkResultLocal.ToolRecord.Points)
	} else {
		log.Printf("[本地Messages] 积分信息缺失: ToolID=%s, Points=nil", checkResultLocal.ToolID)
	}
	proxyInfo.JSONRPCVersion = request.JSONRPC
	proxyInfo.RequestMethod = request.Method
	proxyInfo.RequestTime = time.Now()
	proxyInfo.RequestParams = request.Params

	if request.ID != nil {
		proxyInfo.RequestID = request.ID
	}

	if request.Method == "initialize" {
		paramsB, _ := json.Marshal(request.Params)
		params := &jsonrpc.InitializeParams{}
		if err := json.Unmarshal(paramsB, params); err != nil {
			return ctx.JSONRPCError(jsonrpc.ErrorParseError, nil)
		}

		proxyInfo.ClientName = params.ClientInfo.Name
		proxyInfo.ClientVersion = params.ClientInfo.Version
		proxyInfo.ProtocolVersion = params.ProtocolVersion

		session.SetProxyInfo(proxyInfo)
		ctx.StoreSession(sessionID, session)
	}

	// 进程复用逻辑，与mcp.go保持一致
	serverConfig := session.ServerConfig()
	client, err := getOrCreateMCPClient(ctx, sseKey, serverConfig, session)
	if err != nil {
		fmt.Printf("获取MCP客户端失败: %v\n", err)
		return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
	}

	if client == nil {
		return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
	}

	response, err := client.ForwardMessage(request)

	// 记录请求完成时间和结果（无论成功失败）
	proxyInfo.ResponseTime = time.Now()
	proxyInfo.CostTime = proxyInfo.ResponseTime.Sub(proxyInfo.RequestTime).Milliseconds()

	if err != nil {
		// 记录错误信息
		proxyInfo.ResponseError = err.Error()

		// 为失败的tools/call创建server_logs
		if proxyInfo.RequestMethod == "tools/call" {
			if err := createServerLogAndUpdateUsage(proxyInfo); err != nil {
				log.Printf("[Messages] 创建失败日志记录失败: %v", err)
			}
		}

		handleClientError(ctx, sseKey, serverConfig, session, err)
		return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
	}

	if response != nil {
		if request.Method == "initialize" && response.Result != nil {
			resultB, _ := json.Marshal(response.Result)
			result := &jsonrpc.InitializeResult{}
			if err := json.Unmarshal(resultB, result); err != nil {
				fmt.Printf("unmarshal initialize result failed: %v\n", err)
				return ctx.JSONRPCError(jsonrpc.ErrorParseError, request.ID)
			}

			proxyInfo.ServerName = result.ServerInfo.Name
			proxyInfo.ServerVersion = result.ServerInfo.Version

			session.SetProxyInfo(proxyInfo)
			ctx.StoreSession(sessionID, session)

			// 发送 notifications/initialized 通知
			go func() {
				time.Sleep(100 * time.Millisecond) // 短暂延迟确保 initialize 响应已发送
				notificationReq := &jsonrpc.Request{
					BaseRequest: jsonrpc.BaseRequest{
						JSONRPC: jsonrpc.JSONRPC_VERSION,
						Method:  jsonrpc.MethodInitializedNotification,
					},
					Params: map[string]interface{}{},
				}
				_, err := client.ForwardMessage(notificationReq)
				if err != nil {
					log.Printf("发送 notifications/initialized 失败: %v", err)
				} else {
					log.Printf("已发送 notifications/initialized 通知")
				}
			}()
		}

		// not notification message, send sse message
		session.SendMessage(response.String())
	}

	proxyInfo.ResponseResult = response

	// 为成功的tools/call创建server_logs
	if proxyInfo.RequestMethod == "tools/call" {
		log.Printf("[本地Messages] 准备记录server_logs: ToolName=%s, Points=%d", proxyInfo.ToolName, proxyInfo.Points)
		if err := createServerLogAndUpdateUsage(proxyInfo); err != nil {
			log.Printf("[Messages] 创建成功日志记录失败: %v", err)
		} else {
			log.Printf("[本地Messages] 成功记录server_logs，积分: %d", proxyInfo.Points)
		}
	}

	// 检查响应并决定是否扣除积分
	if checkResultLocal.ShouldDeduct {
		if err := deductPointsAfterToolCall(c.Request().Context(), checkResultLocal, response); err != nil {
			log.Printf("本地messages扣积分失败: %v", err)
		}
	}

	proxyInfoB, _ := json.Marshal(proxyInfo)

	fmt.Printf("proxyInfo: %s\n", string(proxyInfoB))

	return ctx.JSONRPCResponse(response)
}
